// ==UserScript==
// @name         YouTube Ad Auto-Bypass (Safari 2025 Optimized)
// @namespace    https://augmentcode.com
// @version      2025.08.29
// @description  Advanced YouTube ad neutralization with 2025 best practices: payload sanitization, anti-detection bypass, intelligent skip mechanisms, and comprehensive enforcement popup suppression
// @match        *://www.youtube.com/*
// @match        *://m.youtube.com/*
// @match        *://youtube.com/*
// @match        *://music.youtube.com/*
// @match        *://youtubei.googleapis.com/*
// @run-at       document-start
// @grant        unsafeWindow
// @grant        GM_addStyle
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_deleteValue
// ==/UserScript==

(() => {
  'use strict';

  // Enhanced configuration with persistence
  const CONFIG = {
    DEBUG: false,
    AGGRESSIVE_MODE: true,
    STEALTH_MODE: true,
    AUTO_SKIP_DELAY: 50,
    MAX_SKIP_ATTEMPTS: 10,
    DETECTION_EVASION: true
  };

  const dlog = (...args) => CONFIG.DEBUG && console.debug('[YT-AB-2025]', ...args);

  // Performance monitoring
  const perf = {
    start: Date.now(),
    adsBlocked: 0,
    skipsPerformed: 0,
    log() {
      if (CONFIG.DEBUG) {
        console.log(`[YT-AB-2025] Runtime: ${Date.now() - this.start}ms, Ads blocked: ${this.adsBlocked}, Skips: ${this.skipsPerformed}`);
      }
    }
  };

  // --- Enhanced CSS: 隐藏弹窗/推广位/播放器广告容器 + 2025 selectors ---
  const css = `
/* 2025 Anti-adblock detection & enforcement popups */
ytd-enforcement-message-view-model,
ytd-enforcement-message-renderer,
ytd-player-error-message-renderer,
ytd-popup-container[dialog][style-target="player"],
tp-yt-paper-dialog[with-backdrop],
#consent-bump,
.ytd-popup-container,
ytd-ad-break-dialog-renderer,
ytd-mealbar-promo-renderer {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  pointer-events: none !important;
}

/* Enhanced player ad blocking */
#player-ads, .ytp-ad-player-overlay, .ytp-ad-overlay-slot,
.ytp-ad-image-overlay, .ytp-ad-overlay-close-button,
.ytp-ad-skip-button, .ytp-ad-skip-button-modern,
.ytp-ad-text, .ytp-ad-preview-text, .ytp-ad-duration-remaining,
.ytp-paid-content-overlay, .ytp-ad-player-overlay-instream-info,
.ytp-ad-button, .ytp-ad-visit-advertiser-button,
.ytp-ad-simple-ad-badge, .ytp-ad-survey, .ytp-ad-overlay-container {
  display: none !important;
  visibility: hidden !important;
}

/* 2025 Feed ads and promoted content */
ytd-display-ad-renderer, ytd-ad-slot-renderer,
ytd-ad-inline-playback-renderer, ytd-video-masthead-ad-v2-renderer,
ytd-promoted-sparkles-web-renderer, ytd-action-companion-ad-renderer,
ytd-reel-shelf-renderer[is-ad], ytd-rich-item-renderer[is-ad],
ytd-banner-promo-renderer, ytd-statement-banner-renderer,
ytd-primetime-promo-renderer, ytd-brand-video-shelf-renderer[is-ad],
ytd-compact-promoted-item-renderer, ytd-promoted-video-renderer {
  display: none !important;
  height: 0 !important;
  min-height: 0 !important;
}

/* Hide ad-related UI elements */
.ytd-player-legacy-desktop-watch-ads-renderer,
.ytd-ads-engagement-panel-content-renderer,
.ytd-ad-slot-renderer, .ytd-in-feed-ad-layout-renderer {
  display: none !important;
}

/* Stealth mode: prevent detection */
body.no-scroll { overflow: auto !important; }
html.no-scroll { overflow: auto !important; }
  `;
  try {
    if (typeof GM_addStyle === 'function') GM_addStyle(css);
    else {
      const s = document.createElement('style');
      s.textContent = css;
      document.head ? document.head.appendChild(s) : document.documentElement.appendChild(s);
    }
  } catch (_) {}

  // --- Enhanced 核心：净化广告字段 with 2025 patterns ---
  const AD_KEYS = new Set([
    'adPlacements', 'playerAds', 'adSlots', 'adBreaks', 'ad_load_flags',
    'ads', 'ad', 'prerollAllowed', 'midroll', 'adSignals', 'adParams',
    'adServingDataComment', 'adBreakParams', 'adVideoId', 'adCpn',
    'adDevice', 'adFormat', 'adClientName', 'adClientVersion',
    'companionAds', 'vmap', 'adTagParameters', 'adsToken', 'adSafetyReason'
  ]);

  // Enhanced sanitization with better detection evasion
  function sanitize(obj, depth = 0) {
    if (!obj || typeof obj !== 'object' || depth > 8) return obj;

    try {
      // Specialized path clearing with enhanced coverage
      if ('playerResponse' in obj) sanitize(obj.playerResponse, depth + 1);
      if ('adPlacements' in obj) obj.adPlacements = [];
      if ('playerAds' in obj) obj.playerAds = [];
      if ('adBreaks' in obj) obj.adBreaks = [];
      if ('companionAds' in obj) obj.companionAds = [];

      for (const k of Object.keys(obj)) {
        const v = obj[k];
        // Enhanced pattern matching for ad-related keys
        if (AD_KEYS.has(k) ||
            /^ad(s|_|$)/i.test(k) ||
            /advertisement/i.test(k) ||
            /promo/i.test(k) ||
            /_ad_/i.test(k) ||
            /adunit/i.test(k)) {
          if (Array.isArray(v)) obj[k] = [];
          else if (typeof v === 'object') obj[k] = {};
          else obj[k] = null;
          perf.adsBlocked++;
          continue;
        }
        if (v && typeof v === 'object') sanitize(v, depth + 1);
      }

      // Enhanced monetization flags
      if ('isMonetized' in obj) obj.isMonetized = false;
      if ('is_monetized' in obj) obj.is_monetized = false;
      if ('hasAds' in obj) obj.hasAds = false;
      if ('adEnabled' in obj) obj.adEnabled = false;

      // Enhanced tracking cleanup
      if ('playbackTracking' in obj && obj.playbackTracking) {
        const tracking = obj.playbackTracking;
        ['adEventUrl', 'adProgressUrl', 'adImpressionUrl', 'adClickUrl'].forEach(key => {
          if (tracking[key]) delete tracking[key];
        });
      }
    } catch (e) {
      dlog('Sanitization error:', e);
    }

    return obj;
  }

  // --- Enhanced URL sanitization rules with 2025 endpoints ---
  function shouldSanitizeUrl(url = '') {
    if (!url) return false;
    try {
      const u = new URL(url, location.href);

      // Expanded host matching for YouTube ecosystem
      const validHosts = /youtube\.com$|youtube\.com:|googlevideo\.com$|googleapis\.com$|gstatic\.com$/;
      if (!validHosts.test(u.host)) return false;

      // Enhanced endpoint detection including 2025 patterns
      const criticalPaths = [
        '/youtubei/v1/player', '/youtubei/v1/next', '/youtubei/v1/browse',
        '/youtubei/v1/reel_watch_sequence', '/youtubei/v1/guide',
        '/youtubei/v1/search', '/youtubei/v1/get_video_info',
        '/api/stats/ads', '/api/stats/atr', '/ptracking',
        '/player', '/next', '/browse', '/guide'
      ];

      return criticalPaths.some(path =>
        u.pathname.includes(path) || u.pathname.endsWith(path)
      );
    } catch {
      return false;
    }
  }

  // Anti-detection: randomize timing and behavior
  const getRandomDelay = () => Math.floor(Math.random() * 100) + 50;
  const shouldBypassDetection = () => CONFIG.STEALTH_MODE && Math.random() > 0.1;

  // --- 往页面上下文注入补丁（保证 patch 影响到站内脚本） ---
  function inject(code) {
    const s = document.createElement('script');
    s.textContent = code;
    (document.head || document.documentElement).appendChild(s);
    s.parentNode && s.parentNode.removeChild(s);
  }

  function getPagePatchCode() {
    return `(() => {
      const dlog = ${DEBUG ? '(...a)=>console.debug("[YT-AB:page]",...a)' : '()=>{}'};

      const AD_KEYS = new Set(${JSON.stringify(Array.from(AD_KEYS))});
      function sanitize(obj, depth = 0) {
        if (!obj || typeof obj !== 'object' || depth > 6) return obj;
        if ('playerResponse' in obj) sanitize(obj.playerResponse, depth + 1);
        if ('adPlacements' in obj) obj.adPlacements = [];
        if ('playerAds' in obj) obj.playerAds = [];
        if ('adBreaks' in obj) obj.adBreaks = [];
        for (const k of Object.keys(obj)) {
          const v = obj[k];
          if (AD_KEYS.has(k) || /^ad(s|_|$)/i.test(k)) {
            if (Array.isArray(v)) obj[k] = [];
            else if (typeof v === 'object') obj[k] = {};
            else obj[k] = null;
            continue;
          }
          if (v && typeof v === 'object') sanitize(v, depth + 1);
        }
        if ('isMonetized' in obj) obj.isMonetized = false;
        if ('is_monetized' in obj) obj.is_monetized = false;
        return obj;
      }
      function shouldSanitizeUrl(url = '') {
        if (!url) return false;
        try {
          const u = new URL(url, location.href);
          if (!/youtube\\.com$|youtube\\.com:/.test(u.host) && !/googlevideo\\.com$/.test(u.host)) return false;
          return (
            u.pathname.includes('/youtubei/v1/player') ||
            u.pathname.includes('/youtubei/v1/next') ||
            u.pathname.includes('/youtubei/v1/browse') ||
            u.pathname.includes('/youtubei/v1/reel_watch_sequence') ||
            u.pathname.endsWith('/player') ||
            u.pathname.endsWith('/next') ||
            u.pathname.endsWith('/browse')
          );
        } catch {
          return false;
        }
      }

      // 1) 保护 ytInitialPlayerResponse / ytInitialData setter（最早阶段）
      try {
        const protect = (prop) => {
          const desc = Object.getOwnPropertyDescriptor(window, prop);
          let store;
          Object.defineProperty(window, prop, {
            configurable: true,
            enumerable: true,
            get() { return store; },
            set(v) {
              try { store = sanitize(v); }
              catch { store = v; }
            }
          });
          // 如果之前已经有值，尽量读取净化后回写
          if (desc && desc.value) {
            window[prop] = desc.value;
          }
        };
        protect('ytInitialPlayerResponse');
        protect('ytInitialData');
      } catch (e) { dlog('setter patch error', e); }

      // 2) fetch/Response 原型级补丁（覆盖 json() / text() 输出）
      try {
        const origFetch = window.fetch;
        const origJson = Response.prototype.json;
        const origText = Response.prototype.text;

        // 给 Response 打标签（是否需要净化）
        function tagResponse(res, url) {
          try {
            if (shouldSanitizeUrl(url || res.url)) {
              Object.defineProperty(res, '__ytab_patch__', { value: true, writable: false });
            }
          } catch {}
          return res;
        }

        window.fetch = new Proxy(origFetch, {
          apply(target, thisArg, args) {
            const [input] = args;
            const url = typeof input === 'string' ? input : (input && input.url);
            return Reflect.apply(target, thisArg, args).then((res) => tagResponse(res, url));
          }
        });

        Response.prototype.json = new Proxy(origJson, {
          apply(target, thisArg, args) {
            return Reflect.apply(target, thisArg, args).then((data) => {
              try {
                if (thisArg && thisArg.__ytab_patch__) {
                  return sanitize(data);
                }
              } catch {}
              return data;
            });
          }
        });

        Response.prototype.text = new Proxy(origText, {
          apply(target, thisArg, args) {
            return Reflect.apply(target, thisArg, args).then((txt) => {
              try {
                if (thisArg && thisArg.__ytab_patch__) {
                  // 大多数目标接口返回 JSON
                  try {
                    const data = JSON.parse(txt);
                    return JSON.stringify(sanitize(data));
                  } catch { /* 非 JSON 保持原样 */ }
                }
              } catch {}
              return txt;
            });
          }
        });
      } catch (e) { dlog('fetch/Response patch error', e); }

      // 3) 保守的 JSON.parse 兜底（仅用于页面中解析 API JSON 的路径）
      try {
        const origParse = JSON.parse;
        JSON.parse = new Proxy(origParse, {
          apply(target, thisArg, args) {
            const [str] = args;
            let out = Reflect.apply(target, thisArg, args);
            // 仅当像是 player/next/browse 结构时进行净化，避免全局降速
            try {
              if (out && typeof out === 'object') {
                const looksRelevant =
                  'playerResponse' in out ||
                  'adPlacements' in out ||
                  (out?.contents && out?.responseContext) ||
                  (out?.frameworkUpdates && out?.responseContext);
                if (looksRelevant) out = sanitize(out);
              }
            } catch {}
            return out;
          }
        });
      } catch (e) { dlog('JSON.parse patch error', e); }

      dlog('page patches installed');
    })();`;
  }

  // 注入页面补丁
  try {
    inject(getPagePatchCode());
  } catch (e) {
    dlog('inject failed', e);
  }

  // --- Enhanced DOM manipulation: intelligent ad skipping with 2025 techniques ---
  let skipAttempts = 0;
  let lastAdSkipTime = 0;

  function trySkipAds() {
    const now = Date.now();

    // Rate limiting to avoid detection
    if (now - lastAdSkipTime < CONFIG.AUTO_SKIP_DELAY) return;
    lastAdSkipTime = now;

    // 1) Enhanced button detection and clicking with stealth
    const skipSelectors = [
      '.ytp-ad-skip-button', '.ytp-ad-skip-button-modern',
      '.ytp-ad-overlay-close-button', '.ytp-ad-skip-button-container button',
      '[class*="skip"][class*="button"]', '.ytp-ad-skip-button-slot',
      'button[aria-label*="Skip"]', 'button[aria-label*="skip"]'
    ];

    for (const sel of skipSelectors) {
      const elements = document.querySelectorAll(sel);
      elements.forEach(el => {
        if (el && el.offsetParent && !el.disabled) {
          // Stealth clicking with human-like behavior
          if (CONFIG.STEALTH_MODE) {
            setTimeout(() => {
              el.dispatchEvent(new MouseEvent('click', { bubbles: true, cancelable: true }));
              perf.skipsPerformed++;
              dlog('Stealth clicked:', sel);
            }, getRandomDelay());
          } else {
            el.click();
            perf.skipsPerformed++;
            dlog('Direct clicked:', sel);
          }
        }
      });
    }

    // 2) Enhanced ad detection and manipulation
    const player = document.querySelector('.html5-video-player');
    const video = document.querySelector('video');
    const isAd = player?.classList?.contains('ad-showing') ||
                 player?.classList?.contains('ad-interrupting') ||
                 document.querySelector('.ytp-ad-player-overlay');

    if (isAd && video && skipAttempts < CONFIG.MAX_SKIP_ATTEMPTS) {
      skipAttempts++;
      try {
        // Aggressive ad skipping
        if (CONFIG.AGGRESSIVE_MODE) {
          video.playbackRate = 16;
          video.volume = 0;

          // Multiple skip strategies
          if (isFinite(video.duration) && video.duration > 1) {
            video.currentTime = Math.max(0, video.duration - 0.1);
          }

          // Force end event
          setTimeout(() => {
            video.dispatchEvent(new Event('ended'));
          }, 100);
        }

        dlog('Ad manipulation applied, attempt:', skipAttempts);
      } catch (e) {
        dlog('Ad skip error:', e);
      }
    } else if (!isAd && video && video.playbackRate !== 1) {
      // Reset to normal playback
      try {
        video.playbackRate = 1;
        video.volume = 1;
        skipAttempts = 0;
      } catch {}
    }

    // 3) Remove ad overlays and popups
    const adOverlays = document.querySelectorAll([
      '.ytp-ad-overlay-container', '.ytp-ad-text-overlay',
      '.ytp-ad-image-overlay', '[class*="ad-overlay"]'
    ].join(','));

    adOverlays.forEach(overlay => {
      if (overlay.parentNode) {
        overlay.parentNode.removeChild(overlay);
      }
    });
  }

  // --- Enhanced MutationObserver with intelligent throttling ---
  let observerActive = true;
  let mutationCount = 0;
  const maxMutationsPerSecond = 50;

  const mo = new MutationObserver((mutations) => {
    if (!observerActive) return;

    mutationCount++;

    // Throttle excessive mutations to prevent performance issues
    if (mutationCount > maxMutationsPerSecond) {
      setTimeout(() => { mutationCount = 0; }, 1000);
      return;
    }

    // Check if mutations are ad-related
    const hasAdMutations = mutations.some(mutation => {
      return Array.from(mutation.addedNodes).some(node => {
        if (node.nodeType === 1) { // Element node
          const className = node.className || '';
          const id = node.id || '';
          return /ad|promo|overlay/i.test(className + id);
        }
        return false;
      });
    });

    if (hasAdMutations || Math.random() > 0.7) {
      if (CONFIG.STEALTH_MODE) {
        setTimeout(trySkipAds, getRandomDelay());
      } else {
        trySkipAds();
      }
    }
  });

  const startObserve = () => {
    try {
      mo.observe(document.documentElement, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['class', 'style', 'hidden']
      });
      dlog('Enhanced MutationObserver started');
    } catch (e) {
      dlog('Observer start failed:', e);
    }
  };

  // --- Enhanced SPA navigation handling with multiple event types ---
  const navigationHandlers = new Set();

  const rearm = (eventType) => {
    dlog('Navigation detected:', eventType);

    // Reset state on navigation
    skipAttempts = 0;
    mutationCount = 0;

    // Delayed execution with randomization
    const delay = CONFIG.STEALTH_MODE ? getRandomDelay() + 200 : 100;
    setTimeout(() => {
      trySkipAds();
      perf.log();
    }, delay);
  };

  // Enhanced event listeners for YouTube's various navigation systems
  const events = [
    'yt-navigate-finish', 'yt-navigate-start', 'yt-page-data-updated',
    'spfdone', 'spfprocess', 'popstate', 'pushstate',
    'yt-player-updated', 'loadstart'
  ];

  events.forEach(eventName => {
    window.addEventListener(eventName, () => rearm(eventName));
  });

  // Periodic cleanup and maintenance
  setInterval(() => {
    if (CONFIG.DEBUG) perf.log();

    // Restart observer if it seems stuck
    if (mutationCount === 0 && observerActive) {
      mo.disconnect();
      startObserve();
    }
  }, 30000);

  // Initialize everything
  const initialize = () => {
    startObserve();
    trySkipAds();
    dlog('YouTube Ad Auto-Bypass 2025 initialized');
  };

  // Multi-stage initialization
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initialize);
  } else {
    initialize();
  }

  // Fallback initialization
  setTimeout(initialize, 1000);

  // --- Advanced anti-detection measures ---
  if (CONFIG.DETECTION_EVASION) {
    // Randomize script execution timing
    const randomizeExecution = () => {
      const methods = [trySkipAds];
      const method = methods[Math.floor(Math.random() * methods.length)];
      setTimeout(method, Math.random() * 2000 + 500);
    };

    // Periodic randomized execution
    setInterval(randomizeExecution, 15000 + Math.random() * 10000);

    // Hide script presence
    try {
      Object.defineProperty(window, 'ytAdBlockerActive', {
        get: () => false,
        set: () => {},
        configurable: false
      });
    } catch {}
  }

  // --- Cleanup and error recovery ---
  window.addEventListener('beforeunload', () => {
    try {
      mo.disconnect();
      observerActive = false;
      dlog('Cleanup completed');
    } catch {}
  });

  // Global error handler for script resilience
  window.addEventListener('error', (e) => {
    if (e.filename && e.filename.includes('userscript')) {
      dlog('Script error caught:', e.message);
      // Attempt recovery
      setTimeout(() => {
        try {
          initialize();
        } catch {}
      }, 5000);
    }
  });

  // Final status report
  dlog('YouTube Ad Auto-Bypass 2025 Optimized - Fully loaded and active');
  if (CONFIG.DEBUG) {
    console.log('%c[YT-AB-2025] Advanced YouTube Ad Blocker Active',
                'color: #00ff00; font-weight: bold; font-size: 14px;');
  }
})();

